import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Clock, Upload, FileText, DollarSign, ExternalLink } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import type { IncomeReviewStepProps } from '@/types/familyJourney';

export default function IncomeReviewStep({ onSubmit, isCompleted = false }: IncomeReviewStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Income review submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-black rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-black'}`} />
              <span className="text-lg font-semibold">3.3 Review Income</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-black text-white'}>
                {isCompleted ? 'Completed' : 'Active'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="space-y-8">
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <Label htmlFor="income-type" className="text-base font-semibold text-black">
                        Main Income Type:
                      </Label>
                      <Select>
                        <SelectTrigger className="mt-2 text-base">
                          <SelectValue placeholder="Select income type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="employed">Paid Employee (Loondienst)</SelectItem>
                          <SelectItem value="owner">Owner-Director of a BV</SelectItem>
                          <SelectItem value="self-employed">Self-employed</SelectItem>
                          <SelectItem value="private">Private Equity</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="main-parent" className="text-base font-semibold text-black">
                        Which parent provides the main income:
                      </Label>
                      <Select>
                        <SelectTrigger className="mt-2 text-base">
                          <SelectValue placeholder="Select parent" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="parent1">Parent 1</SelectItem>
                          <SelectItem value="parent2">Parent 2</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="bg-stone-50 border-2 border-stone-200 p-6 rounded-md">
                    <div className="flex items-center space-x-3 mb-4">
                      <DollarSign className="h-6 w-6 text-black" />
                      <h4 className="font-bold text-black text-base">Income Requirements</h4>
                    </div>
                    <p className="text-stone-700 text-base">
                      A Host Family must have sufficient, independent, and sustainable income above a certain threshold.
                      <a href="#" className="underline ml-2 text-black font-semibold">
                        Check the current income requirement here
                      </a>
                    </p>
                  </div>

                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="bg-black text-white p-6 rounded-md text-center">
                      <FileText className="h-10 w-10 mx-auto mb-4" />
                      <h4 className="font-bold mb-4 text-lg">Employer&apos;s Declaration</h4>
                      <Button variant="secondary" size="sm" className="mb-4 bg-white text-black hover:bg-stone-100">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        DOWNLOAD
                      </Button>
                      <div className="border-2 border-dashed border-white/30 rounded-md p-4">
                        <Upload className="h-6 w-6 mx-auto text-white/70" />
                      </div>
                    </div>

                    <div className="bg-stone-900 text-white p-6 rounded-md text-center">
                      <FileText className="h-10 w-10 mx-auto mb-4" />
                      <h4 className="font-bold mb-4 text-lg">Employment Agreement</h4>
                      <div className="border-2 border-dashed border-white/30 rounded-md p-8">
                        <Upload className="h-6 w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload file</p>
                      </div>
                    </div>

                    <div className="bg-stone-700 text-white p-6 rounded-md text-center">
                      <FileText className="h-10 w-10 mx-auto mb-4" />
                      <h4 className="font-bold mb-4 text-lg">3 Recent Salary Slips</h4>
                      <div className="border-2 border-dashed border-white/30 rounded-md p-8">
                        <Upload className="h-6 w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload files</p>
                      </div>
                    </div>
                  </div>

                  <Button
                    className="w-full bg-black text-white hover:bg-stone-800 text-lg py-4"
                    onClick={handleSubmit}
                    disabled={isCompleted}
                  >
                    SUBMIT INCOME DOCUMENTATION
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
