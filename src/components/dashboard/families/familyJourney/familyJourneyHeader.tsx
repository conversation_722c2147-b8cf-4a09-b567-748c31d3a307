import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Menu } from 'lucide-react';
import { useSidebarContext } from '@/components/dashboard/sidebar/sideBarContext';
import type { FamilyJourneyHeaderProps } from '@/types/familyJourney';

export function FamilyJourneyHeader({
  currentStageData,
  currentStepData,
  mobileSidebarOpen,
  onMobileSidebarToggle,
  className,
}: FamilyJourneyHeaderProps) {
  const { sidebarCollapsed } = useSidebarContext();

  return (
    <div
      className={cn(
        'fixed top-16 right-0 z-20 border-b bg-background/95 backdrop-blur-sm h-16 transition-all duration-300 ease-in-out',
        'left-0', // Full width on mobile
        sidebarCollapsed ? 'md:left-16' : 'md:left-64', // Adjust left margin based on sidebar state on desktop
        className
      )}
    >
      <div className="container mx-auto px-4 py-3 h-full">
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <div className="min-w-0 flex-1">
              <h1 className="text-lg lg:text-xl font-semibold truncate">{currentStageData.title}</h1>
              <p className="text-sm text-muted-foreground truncate">{currentStepData.title}</p>
            </div>
          </div>

          {/* Mobile Navigation Button - Top Right */}
          <div className="lg:hidden">
            <Button
              variant={mobileSidebarOpen ? 'secondary' : 'outline'}
              size="sm"
              className={cn(
                'h-9 w-9 p-0 transition-all duration-200',
                mobileSidebarOpen
                  ? 'bg-primary/10 border-primary/20 text-primary hover:bg-primary/15'
                  : 'border-border/60 hover:bg-muted/60 hover:border-border'
              )}
              onClick={onMobileSidebarToggle}
              aria-label={mobileSidebarOpen ? 'Close navigation sidebar' : 'Open navigation sidebar'}
            >
              <Menu className={cn('h-4 w-4 transition-transform duration-200', mobileSidebarOpen ? 'rotate-90' : '')} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
