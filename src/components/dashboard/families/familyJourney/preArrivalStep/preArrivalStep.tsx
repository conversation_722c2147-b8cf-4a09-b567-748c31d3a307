import { useState } from 'react';
import RoomPreparationStep from './roomPreparationStep';
import OrientationMaterialsStep from './orientationMaterialsStep';
import type { PreArrivalStepProps } from '@/types/familyJourney';

export default function PreArrivalStep({ currentStep = 0 }: PreArrivalStepProps) {
  const [roomPreparationCompleted, setRoomPreparationCompleted] = useState(false);
  const [orientationMaterialsCompleted, setOrientationMaterialsCompleted] = useState(false);

  const handleRoomPreparationSubmit = () => {
    setRoomPreparationCompleted(true);
    console.log('Room preparation completed');
  };

  const handleOrientationMaterialsSubmit = () => {
    setOrientationMaterialsCompleted(true);
    console.log('Orientation materials completed');
  };

  return (
    <div className="space-y-8">
      <RoomPreparationStep onSubmit={handleRoomPreparationSubmit} isCompleted={roomPreparationCompleted} />

      <OrientationMaterialsStep
        onSubmit={handleOrientationMaterialsSubmit}
        isCompleted={orientationMaterialsCompleted}
      />
    </div>
  );
}
