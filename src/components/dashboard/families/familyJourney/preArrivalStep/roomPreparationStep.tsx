import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Home } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import type { RoomPreparationStepProps } from '@/types/familyJourney';
export default function RoomPreparationStep({ onSubmit, isCompleted = false }: RoomPreparationStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Room preparation step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-blue-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Home className={`h-6 w-6 ${isCompleted ? 'text-blue-500' : 'text-stone-600'}`} />
              <span className="text-lg font-semibold">6.1 Room Preparation</span>
              <Badge className={isCompleted ? 'bg-blue-500 text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending Previous Steps'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <Home className="h-20 w-20 text-blue-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-stone-800 mb-4">Prepare Au Pair Accommodation</h3>
                  <p className="text-stone-600 mb-6 text-lg max-w-md mx-auto">
                    This step will become available once you have completed the travel and visa arrangements.
                  </p>
                  <Button
                    className="bg-blue-500 text-white hover:bg-blue-600"
                    onClick={handleSubmit}
                    disabled={!isCompleted}
                  >
                    START ROOM PREPARATION
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
