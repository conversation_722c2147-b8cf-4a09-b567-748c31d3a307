import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import type { BeginMatchingStepProps } from '@/types/familyJourney';

export default function BeginMatchingStep({ onSubmit, isCompleted = false }: BeginMatchingStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Begin matching step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-stone-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Heart className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-stone-600'}`} />
              <span className="text-lg font-semibold">4.1 Begin Matching</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending Previous Steps'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <Heart className="h-20 w-20 text-pink-600 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-stone-800 mb-4">Ready to Find Your Au Pair</h3>
                  <p className="text-stone-600 mb-6 text-lg max-w-md mx-auto">
                    This step will become available once you have completed all previous steps in the compliance
                    section.
                  </p>
                  <Button
                    className="bg-black text-white hover:bg-stone-800"
                    onClick={handleSubmit}
                    disabled={!isCompleted}
                  >
                    BEGIN MATCHING PROCESS
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
