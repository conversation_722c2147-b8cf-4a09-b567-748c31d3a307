import { useState } from 'react';
import MonthlyCheckInsStep from './monthlyCheckInsStep';
import SupportResourcesStep from './supportResourcesStep';
import type { ProgramStepProps } from '@/types/familyJourney';


export default function ProgramStep({ currentStep = 0 }: ProgramStepProps) {
  const [monthlyCheckInsCompleted, setMonthlyCheckInsCompleted] = useState(false);
  const [supportResourcesCompleted, setSupportResourcesCompleted] = useState(false);

  const handleMonthlyCheckInsSubmit = () => {
    setMonthlyCheckInsCompleted(true);
    console.log('Monthly check-ins completed');
  };

  const handleSupportResourcesSubmit = () => {
    setSupportResourcesCompleted(true);
    console.log('Support resources completed');
  };

  return (
    <div className="space-y-8">
      <MonthlyCheckInsStep onSubmit={handleMonthlyCheckInsSubmit} isCompleted={monthlyCheckInsCompleted} />

      <SupportResourcesStep onSubmit={handleSupportResourcesSubmit} isCompleted={supportResourcesCompleted} />
    </div>
  );
}
