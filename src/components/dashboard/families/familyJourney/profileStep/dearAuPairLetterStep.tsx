import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useState } from 'react';
import type { DearAuPairLetterProps } from '@/types/familyJourney';

export default function DearAuPairLetterStep({
  onSubmit,
  initialContent = '',
  isCompleted = false,
}: DearAuPairLetterProps) {
  const [letterContent, setLetterContent] = useState(initialContent);

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit(letterContent);
    }
    console.log('Au-Pair letter submitted:', letterContent);
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-stone-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-stone-600'}`} />
              <span className="text-lg font-semibold">2.1 Dear Au-Pair Letter</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-base mb-3">To do:</h4>
                      <p className="text-stone-700 text-base leading-relaxed">
                        Please write a short letter. The &apos;Dear Au Pair&apos; letter offers a personal touch that only you, as
                        a host family, can convey to your future au pair.
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-black text-base mb-3">SUGGESTIONS:</h4>
                      <ul className="list-disc list-inside space-y-2 text-stone-700 text-base">
                        <li>
                          Short introduction of your family, parents, kid(s) information about character and likes
                        </li>
                        <li>
                          Description of family situation (where you live, house, garden surroundings, au pair room)
                        </li>
                        <li>Why would you like to invite an au pair in your family</li>
                        <li>What do you expect from your future Au Pair?</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-black text-white p-8 rounded-md">
                    <h3 className="text-base font-bold mb-6">Au-Pair Letter</h3>
                    <Textarea
                      placeholder="Write your letter to future au pairs..."
                      className="min-h-30 bg-white/10 border-white/20 text-white placeholder:text-white/70 text-base"
                      value={letterContent}
                      onChange={e => setLetterContent(e.target.value)}
                    />
                    <Button className="w-full mt-6 bg-white text-black hover:bg-stone-100" onClick={handleSubmit}>
                      SUBMIT
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
