import { useState } from 'react';
import { useParams } from 'react-router';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// Import stages data and configuration
import SidebarContent, { createStageComponentMap, getInitialStepStatus, journeyConfig, stages } from '@/components/dashboard/families/journeySideBar';

import IntakeStep from '@/components/dashboard/families/familyJourney/intakeStep/intakeStep.tsx';
import ArrivalStep from '@/components/dashboard/families/familyJourney/arrivalStep/arrivalStep.tsx';
import ComplianceStep from '@/components/dashboard/families/familyJourney/complianceStep/complianceStep.tsx';
import DepartureStep from '@/components/dashboard/families/familyJourney/departureStep/departureStep.tsx';
import MatchingStep from '@/components/dashboard/families/familyJourney/matchingStep/matchingStep.tsx';
import PreArrivalStep from '@/components/dashboard/families/familyJourney/preArrivalStep/preArrivalStep.tsx';
import ProfileStep from '@/components/dashboard/families/familyJourney/profileStep/profileStep.tsx';
import ProgramStep from '@/components/dashboard/families/familyJourney/programStep/programStep.tsx';
import TravelStep from '@/components/dashboard/families/familyJourney/travelStep/travelStep.tsx';
import { FamilyJourneyHeader } from '@/components/dashboard/families/familyJourney/FamilyJourneyHeader';
import { FamilyJourneyFooter } from '@/components/dashboard/families/familyJourney/FamilyJourneyFooter';

interface StepStatus {
  [key: string]: boolean;
}

interface StageComponentProps {
  currentStep?: number;
}

// Create stage component mapping from configuration
const stageComponentMap = createStageComponentMap({
  IntakeStep,
  ProfileStep,
  ComplianceStep,
  MatchingStep,
  TravelStep,
  PreArrivalStep,
  ArrivalStep,
  ProgramStep,
  DepartureStep,
});

export default function FamilyJourneyPage() {
  const { id } = useParams<{ id: string }>();

  // State for current stage and step
  const [currentStage, setCurrentStage] = useState(1);
  const [currentStep, setCurrentStep] = useState(0);
  const [expandedStages, setExpandedStages] = useState(journeyConfig.ui.stages.defaultExpanded);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(journeyConfig.ui.sidebar.defaultCollapsed);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const [stepStatus, setStepStatus] = useState<StepStatus>(getInitialStepStatus());

  const currentStageData = stages.find(stage => stage.id === currentStage)!;

  const currentStepData = currentStageData.steps[currentStep];

  const nextStep = () => {
    if (currentStep < currentStageData.steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else if (currentStage < stages.length) {
      setCurrentStage(currentStage + 1);
      setCurrentStep(0);
      // Expand the next stage if configured to do so
      if (journeyConfig.behavior.autoExpandNextStage && !expandedStages.includes(currentStage + 1)) {
        setExpandedStages([...expandedStages, currentStage + 1]);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else if (currentStage > 1) {
      setCurrentStage(currentStage - 1);
      setCurrentStep(stages[currentStage - 2].steps.length - 1);
      // Expand the previous stage if configured to do so
      if (journeyConfig.behavior.autoExpandNextStage && !expandedStages.includes(currentStage - 1)) {
        setExpandedStages([...expandedStages, currentStage - 1]);
      }
    }
  };

  // Handler to mark a step as complete and move to next step
  const markStepComplete = (stepId: string) => {
    setStepStatus(prev => ({ ...prev, [stepId]: true }));
    setTimeout(nextStep, journeyConfig.ui.steps.autoAdvanceDelay);
  };

  // Handler to expand/collapse a stage
  const toggleStageExpansion = (stageId: number) => {
    if (expandedStages.includes(stageId)) {
      setExpandedStages(expandedStages.filter(id => id !== stageId));
    } else {
      setExpandedStages([...expandedStages, stageId]);
    }
  };

  // Handler to go to a specific step in a stage
  const goToStep = (stageId: number, stepIndex: number) => {
    setCurrentStage(stageId);
    setCurrentStep(stepIndex);
    setMobileSidebarOpen(false); // Close mobile sidebar when navigating
  };

  // Mobile sidebar handlers
  const openMobileSidebar = () => setMobileSidebarOpen(true);
  const closeMobileSidebar = () => setMobileSidebarOpen(false);

  // Render the content for the current stage using step components
  const renderStageContent = () => {
    const StageComponent = stageComponentMap[currentStage] as React.ComponentType<StageComponentProps>;

    if (StageComponent) {
      return <StageComponent currentStep={currentStep} />;
    }

    // Fallback for stages without dedicated components
    return (
      <div className="space-y-4 lg:space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{currentStageData.title}</CardTitle>
            <CardDescription>{currentStageData.description}</CardDescription>
          </CardHeader>
          <CardContent>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header Component */}
      <FamilyJourneyHeader
        currentStageData={currentStageData}
        currentStepData={currentStepData}
        mobileSidebarOpen={mobileSidebarOpen}
        onMobileSidebarToggle={mobileSidebarOpen ? closeMobileSidebar : openMobileSidebar}
      />

      {/* Main Content Area - adjusted for fixed header and footer */}
      <div className="flex flex-1 overflow-hidden">
        {/* Scrollable Content */}
        <div
          className={cn(
            'flex-1 flex flex-col min-w-0 overflow-y-auto transition-all duration-300',
            'pt-20 pb-24', // Account for fixed header (64px + 16px) and footer (80px + 16px)
            sidebarCollapsed ? 'lg:pr-20' : 'lg:pr-80 xl:pr-96' // Dynamic right padding based on sidebar state
          )}
        >
          <div className="container mx-auto px-4 py-6">{renderStageContent()}</div>
        </div>

        {/* Desktop Sidebar - Fixed Right Side */}
        <div
          className={cn(
            'hidden lg:flex fixed right-0 z-30 border-l border-border/40 bg-gradient-to-b from-background to-muted/5 flex-shrink-0 transition-all duration-300 ease-in-out',
            'shadow-sm backdrop-blur-sm',
            'top-32 bottom-21', // Position below Family Journey header and above footer
            sidebarCollapsed ? 'w-20' : 'w-80 xl:w-96'
          )}
        >
          <SidebarContent
            currentStage={currentStage}
            currentStep={currentStep}
            expandedStages={expandedStages}
            stepStatus={stepStatus}
            toggleStageExpansion={toggleStageExpansion}
            goToStep={goToStep}
            initialCollapsed={sidebarCollapsed}
            onCollapsedChange={setSidebarCollapsed}
          />
        </div>
      </div>

      {/* Mobile Sidebar - Slide-out from right */}
      <div className="lg:hidden">
        {/* Backdrop */}
        {mobileSidebarOpen && (
          <div
             className="fixed inset-0 z-40 bg-black/50"
             onClick={closeMobileSidebar}
             aria-label="Close sidebar backdrop"
          />
        )}

        {/* Sidebar Panel - positioned below fixed header and above fixed footer */}
        <div
          className={cn(
            'fixed right-0 z-40 w-[80%] rounded-l-lg max-w-sm',
            'top-16 bottom-1', // Position below Family Journey header and above footer
            'transition-transform duration-300 ease-in-out overflow-hidden flex flex-col',
            mobileSidebarOpen ? 'translate-x-0' : 'translate-x-full'
          )}
        >
          <SidebarContent
            currentStage={currentStage}
            currentStep={currentStep}
            expandedStages={expandedStages}
            stepStatus={stepStatus}
            toggleStageExpansion={toggleStageExpansion}
            goToStep={goToStep}
            isMobileMenuOpen={mobileSidebarOpen}
            closeMobileMenu={closeMobileSidebar}
          />
        </div>
      </div>

      {/* Fixed Footer Component */}
      <FamilyJourneyFooter
        currentStage={currentStage}
        currentStep={currentStep}
        currentStageData={currentStageData}
        currentStepData={currentStepData}
        stepStatus={stepStatus}
        journeyConfig={journeyConfig}
        onPrevStep={prevStep}
        onMarkStepComplete={markStepComplete}
      />
    </div>
  );
}
