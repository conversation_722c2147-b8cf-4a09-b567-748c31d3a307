// 1. Base Interfaces
export interface BaseStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
  isExpanded?: boolean;
}

export interface BaseJourneyStepProps {
  currentStep?: number;
}

// 2. Data Models
export interface Child {
  name: string;
  age: string;
  gender: string;
}

export interface FamilyFormData {
  familyName: string;
  address: string;
  postalCode: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  children: Child[];
  preferredStartDate: string;
  latestStartDate: string;
}

// 3. Journey Structure Types
export interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  steps: Step[];
  componentName: string;
  borderColor: string;
  badgeColor: string;
}

export interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
  defaultStatus: boolean;
}

// 4. Journey Navigation Types
export interface JourneyConfig {
  ui: {
    navigation: {
      showPreviousButton: boolean;
      showNextButton: boolean;
      showStepCounter: boolean;
      previousButtonText: string;
      nextButtonText: string;
      doneButtonText: string;
    };
  };
}

export interface StepStatus {
  [key: string]: boolean;
}

// 5. Component-Specific Interfaces
export interface FamilyJourneyHeaderProps {
  currentStageData: Stage;
  currentStepData: Step;
  mobileSidebarOpen: boolean;
  onMobileSidebarToggle: () => void;
  className?: string;
}

export interface FamilyJourneyFooterProps {
  currentStage: number;
  currentStep: number;
  currentStageData: Stage;
  currentStepData: Step;
  stepStatus: StepStatus;
  journeyConfig: JourneyConfig;
  onPrevStep: () => void;
  onMarkStepComplete: (stepId: string) => void;
  className?: string;
}

export interface StageComponentProps {
  currentStep?: number;
}

export interface SidebarContentProps {
  currentStage: number;
  currentStep: number;
  expandedStages: number[];
  stepStatus: StepStatus;
  toggleStageExpansion: (stageId: number) => void;
  goToStep: (stageId: number, stepIndex: number) => void;
  isMobileMenuOpen?: boolean;
  className?: string;
  onCollapsedChange?: (collapsed: boolean) => void;
  initialCollapsed?: boolean;
  closeMobileMenu?: () => void;
}

// 6. Step Component Interfaces
export interface DearAuPairLetterProps extends BaseStepProps {
  initialContent?: string;
}

export interface FamilyPicturesProps extends Omit<BaseStepProps, 'onSubmit'> {
  onSubmit?: (files: File[]) => void;
  uploadedFiles?: File[];
}

// 7. Step Implementation Interfaces
export type WeeklyScheduleProps = BaseStepProps;
export type RoomPreparationStepProps = BaseStepProps;
export type OrientationMaterialsStepProps = BaseStepProps;
export type CandidateReviewStepProps = BaseStepProps;
export type BeginMatchingStepProps = BaseStepProps;
export type IntakeMeetingStepProps = BaseStepProps ;
export type AssignmentConfirmationStepProps = BaseStepProps ;
export type FinalEvaluationStepProps = BaseStepProps ;
export type DepartureChecklistStepProps = BaseStepProps ;
export type ProofOfAddressStepProps = BaseStepProps ;
export type IncomeReviewStepProps = BaseStepProps ;
export type FirstWeekSetupStepProps = BaseStepProps ;
export type AirportPickupStepProps = BaseStepProps ;
export type LegalResidenceStepProps = BaseStepProps ;
export type TravelArrangementsStepProps = BaseStepProps ;
export type MonthlyCheckInsStepProps = BaseStepProps ;
export type SupportResourcesStepProps = BaseStepProps ;

// 8. Journey Step Interfaces
export type VisaApplicationStepProps = BaseJourneyStepProps ;
export type TravelStepProps = BaseJourneyStepProps ;
export type ProgramStepProps = BaseJourneyStepProps ;
export type ProfileStepProps = BaseJourneyStepProps ;
export type PreArrivalStepProps = BaseJourneyStepProps ;
export type MatchingStepProps = BaseJourneyStepProps ;
export type IntakeStepProps = BaseJourneyStepProps ;
export type ComplianceStepProps = BaseJourneyStepProps ;
export type ArrivalStepProps = BaseJourneyStepProps ;
export type DepartureStepProps = BaseJourneyStepProps ;